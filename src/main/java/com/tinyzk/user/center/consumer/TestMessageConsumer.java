package com.tinyzk.user.center.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 测试消息消费者
 * 用于验证RocketMQ消息消费功能
 */
@Component
@Slf4j
@RocketMQMessageListener(
    topic = "TEST_TOPIC",
    consumerGroup = "test-consumer-group",
    consumeMode = org.apache.rocketmq.spring.annotation.ConsumeMode.CONCURRENTLY,
    maxReconsumeTimes = 3,
    consumeThreadMax = 10,
    messageModel = org.apache.rocketmq.spring.annotation.MessageModel.CLUSTERING,
    selectorExpression = "*"
)
public class TestMessageConsumer implements RocketMQListener<String> {

    @Override
    public void onMessage(String message) {
        try {
            log.info("🎉 收到测试消息: {}", message);
            log.info("✅ 测试消息消费成功");
            
            // 模拟消息处理
            Thread.sleep(100);
            
        } catch (Exception e) {
            log.error("❌ 测试消息消费失败", e);
            throw new RuntimeException("消息消费失败", e);
        }
    }
}
