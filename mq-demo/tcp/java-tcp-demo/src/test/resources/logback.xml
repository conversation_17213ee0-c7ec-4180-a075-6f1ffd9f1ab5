<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="defaultLogAppener"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${user.home}/logs/test.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${user.home}/logs/otherdays/test.%i.log
            </fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>10</maxIndex>
        </rollingPolicy>
        <triggeringPolicy
                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <logger name="defaultLog" additivity="false">
        <level value="INFO" />
        <appender-ref ref="defaultLogAppener" />
    </logger>

</configuration>
