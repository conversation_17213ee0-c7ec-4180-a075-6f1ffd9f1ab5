<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <import resource="common.xml"/>

    <bean id="producer" class="com.aliyun.openservices.ons.api.bean.OrderProducerBean" init-method="start"
          destroy-method="shutdown">
        <property name="properties"> <!--顺序生产者配置信息-->
            <props>
                <prop key="GROUP_ID">${GROUP_ID}</prop> <!--请替换为自己的账户信息-->
                <prop key="AccessKey">${AccessKey}</prop>
                <prop key="SecretKey">${SecretKey}</prop>
                <prop key="NAMESRV_ADDR">${NAMESRV_ADDR}</prop>
            </props>
        </property>
    </bean>
</beans>