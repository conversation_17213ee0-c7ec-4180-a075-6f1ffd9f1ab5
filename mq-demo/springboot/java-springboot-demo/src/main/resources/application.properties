#启动测试之前请替换如下 XXX 为您的配置

#如果使用当前Demo访问阿里云RocketMQ 4.0系列实例，请设置访问的阿里云账号的AccessKeyId。
#如果使用当前Demo访问阿里云RocketMQ 5.0系列实例，请设置实例详情页获取的实例用户名，不要设置阿里云账号的AccessKeyId。
rocketmq.accessKey=XXX

#如果使用当前Demo访问阿里云RocketMQ 4.0系列实例，请设置访问的阿里云账号的AccessKeySecret。
#如果使用当前Demo访问阿里云RocketMQ 5.0系列实例，请设置实例详情页获取的实例密码，不要设置阿里云账号的AccessKeySecret。
rocketmq.secretKey=XX

#NAMESRV_ADDR, 通过"实例管理--获取接入点信息--TCP协议接入点"获取。
#如果使用当前Demo访问阿里云RocketMQ 4.0系列实例，接入点应该是类似这样的格式  http://MQ_INST_XXX:xxx，注意！！！一定要有http协议头
#如果使用当前Demo访问阿里云RocketMQ 5.0系列实例，接入点应该是类似这样的格式  rmq-cn-xxx.xx:xxx，注意！！！一定不要自己添加http协议头
#
rocketmq.nameSrvAddr=XXX

# 设置 Topic 前需要在RocketMQ控制台提前创建。需要注意的是，每个Topic仅支持一种消息类型，不要混用。
rocketmq.topic=XXX
# 设置 GroupID前需要在RocketMQ控制台提前创建ConsumerGroup。
rocketmq.groupId=XXX
rocketmq.tag=*

# 设置 Topic 前需要在RocketMQ控制台提前创建。需要注意的是，每个Topic仅支持一种消息类型，不要混用。
rocketmq.orderTopic=XXX
# 设置 GroupID前需要在RocketMQ控制台提前创建ConsumerGroup。
rocketmq.orderGroupId=XXX
rocketmq.orderTag=*
